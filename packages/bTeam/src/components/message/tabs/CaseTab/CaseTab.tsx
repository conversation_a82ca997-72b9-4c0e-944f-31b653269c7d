import React from "react";
import { StyleSheet, View } from "react-native";

// Components
import {
  Theme,
  useThemeAwareObject,
  SPACING,
  MessageCaseList,
  MessageCaseMetadataList,
  CustomText,
  FONT_SIZES,
  TabTitle,
} from "b-ui-lib";
import EmptyTabBody from "../../../general/EmptyTabBody";
import MessageBottomButtons from "../../../general/MessageBottomButtons";

type Props = {
  caseMetadata: any;
  cases: any;
};

const CaseTab: React.FC = ({ caseMetadata, cases }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  const BUTTONS = [
    {
      title: "Link to a Case",
      onPress: () => {},
    },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.body}>
        <TabTitle title="Metadata" count={caseMetadata?.length} />

        {caseMetadata && caseMetadata?.length > 0 ? (
          <MessageCaseMetadataList caseMetadata={caseMetadata} />
        ) : (
          <CustomText style={styles.emptyMetadataText}>
            No metadata available
          </CustomText>
        )}

        <TabTitle title="Cases" count={cases.length} />

        {cases && cases?.length > 0 ? (
          <MessageCaseList cases={cases} />
        ) : (
          <EmptyTabBody
            iconName="briefcase"
            emptyMessage="Not linked to any case yet"
          />
        )}
      </View>

      {cases && cases?.length > 0 && <MessageBottomButtons buttons={BUTTONS} />}
    </View>
  );
};

export default CaseTab;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "space-between",
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    body: {
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      paddingTop: SPACING.M,
      paddingHorizontal: SPACING.M,
      gap: SPACING.SIX,
    },
    emptyMetadataText: {
      alignSelf: "center",
      color: color.HALF_DIMMED,
      fontSize: FONT_SIZES.TWELVE,
    },
  });

  return { styles, color };
};
