import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { getMessageBody, getMessageBodyFailed, getMessageBodySuccess } from "../slices/gridMessageSlice";

export const fetchMessageBody = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainMessageUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainMessageUrl);
  const request$ = sources.ACTION.filter(
    (action) => action.type === getMessageBody.type)
    .compose(sampleCombine(token$, domainMessageUrl$))
    .map(([action, token, domainMessageUrl]) => {
      const { UMS_Guid } = action?.payload || {};

      return {
        url: `${domainMessageUrl}/index.aspx?UMS_Guid=${UMS_Guid}`,
        category: "getMessageBody",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        send: { UMS_Guid }
      };
    });

  const response$ = sources.HTTP.select("getMessageBody")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action$ = xs
    .combine(response$)
    .map(([response]) => {
      const UMS_Guid = response?.request?.send?.UMS_Guid;
      const messageBody = response?.text;

      return getMessageBodySuccess({ UMS_Guid, messageBody });
    });

  return {
    ACTION: action$,
    HTTP: request$,
  };
};

export const fetchMessageBodyFailed = (sources) => {
  const response$ = sources.HTTP.select("getMessageBody")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getMessageBodyFailed(arr));

  return {
    ACTION: action$,
  };
};
