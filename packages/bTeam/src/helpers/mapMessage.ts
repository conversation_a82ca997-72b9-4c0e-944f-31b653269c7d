import { GridMessageDto } from "../types/DTOs/gridMessageDto";
import { calculateAvatarName } from "./calculateAvatarName";
import { AttachmentDTO } from "../types/DTOs/AttachmentDTO";
import { Message } from "../types/message";
import { GridMessageActionsDto } from "../types/DTOs/gridMessageActionsDto";
import { CaseMessageDTO } from "../types/DTOs/caseMessageDTO";

export const mapMessage = (
  message: GridMessageDto,
  oldMessage: Message
): Message => {
  return {
    ...oldMessage,
    id: message.UMS_Guid,
    messageKey: message.MSG_Key,
    sentDate: message.MSG_DateTime,
    username: message.msgUser,
    from: message.MSG_From,
    to: message.MSG_To,
    subject: message.MSG_Subject,
    body: message.msgBodyText,
    avatarName: calculateAvatarName(message.msgUser),
    isFlagged: message.umsIsFlagged,
    attachmentsCount: message.MSG_AttachmentsCount,
    inOut: message.MSG_InOut,
    type: message.MSG_MST_Id,
    isViewed: message.umsIsViewed,
    msgUserReplied: message.msgUserReplied,
    hasReplies: Boolean(message.msgUserReplied),
    hasAttachments: message.MSG_AttachmentsCount > 0,
    hasComments: Boolean(
      message.metadataCommentInfoBits === 2 ||
        message.metadataCommentInfoBits === 3
    ),
    hasMetadata: Boolean(
      message.metadataCommentInfoBits === 1 ||
        message.metadataCommentInfoBits === 3
    ),
    hasFolders: Boolean(message.msgInfoBits === 1 || message.msgInfoBits === 3),
    hasCases: Boolean(message.msgInfoBits === 2 || message.msgInfoBits === 3),

    // These fields are populated by different requests, so I want to preserve their existing values or initialize them.
    fullMessageBody: oldMessage?.fullMessageBody || "",
    sourceFolderDeletedFrom: oldMessage?.sourceFolderDeletedFrom || "",
    messageCommentIds: oldMessage?.messageCommentIds || [],
    messageMetadata: oldMessage?.messageMetadata || [],
    fromDisplayInfo: oldMessage?.fromDisplayInfo,
    tos: oldMessage?.tos || [],
    cc: oldMessage?.cc || "",
    bcc: oldMessage?.bcc || "",
    ccs: oldMessage?.ccs || [],
    bccs: oldMessage?.bccs || [],
    repliedByUser: oldMessage?.repliedByUser || "",
    timeReplied: oldMessage?.timeReplied || "",
    attachmentsIds: oldMessage?.attachmentsIds || [],
    foldersIds: oldMessage?.foldersIds || [],
    commentMessageUsers: oldMessage?.commentMessageUsers || [],
    caseIds: oldMessage?.caseIds || [],
  };
};

export const mapMessageActions = (
  response: GridMessageActionsDto,
  oldMessage: Message
) => {
  return {
    ...oldMessage,
    MSG_Guid: response?.MSG_Guid,
    fromDisplayInfo: response.From,
    tos: response?.Tos,
    cc: response?.MSG_Cc,
    bcc: response?.MSG_Bcc,
    ccs: response?.Ccs,
    bccs: response?.Bccs,
    repliedByUser: response?.repliedByUser,
    timeReplied: response?.MSG_TimeReplied,
    attachmentsIds: Array.from(
      response?.Attachments?.map((attachment) => attachment.FLN_Guid) || []
    ),
    foldersIds: Array.from(
      response?.FolderMessageInfo?.map((folder) => folder.FLM_Guid) || []
    ),
    caseIds: Array.from(
      response?.Cases?.map((caseMessage) => caseMessage.CAS_Guid) || []
    ),
  };
};

export const mapMessageAttachments = (
  response: GridMessageActionsDto,
  oldAttachments: Message
) => {
  return {
    byId: {
      ...oldAttachments?.byId,
      ...response?.Attachments?.reduce((acc, attachment) => {
        acc[attachment.FLN_Guid] = {
          id: attachment.FLN_Guid,
          name: attachment.FLN_FileName,
          messageId: response?.UMS_Guid,
        };
        return acc;
      }, {}),
    },
    allIds: Array.from(
      new Set([
        ...(oldAttachments?.allIds || []),
        ...(response?.Attachments?.map((attachment) => attachment.FLN_Guid) ||
          []),
      ])
    ),
  };
};

export const mapAttachments = (
  response: AttachmentDTO,
  oldAttachments: Message
) => {
  return {
    byId: {
      ...oldAttachments?.byId,
      ...response?.reduce((acc, attachment) => {
        acc[attachment.FLN_Guid] = {
          id: attachment.FLN_Guid,
          name: attachment.FLN_FileName,
          isLoading: false,
        };
        return acc;
      }, {}),
    },
    allIds: Array.from(
      new Set([
        ...(oldAttachments?.allIds || []),
        ...(response?.map((attachment) => attachment.FLN_Guid) || []),
      ])
    ),
  };
};

export const mapMessageFolders = (
  response: GridMessageActionsDto,
  oldMessageFolders: Message
) => {
  return {
    byId: {
      ...oldMessageFolders?.byId,
      ...response?.FolderMessageInfo?.reduce((acc, folder) => {
        acc[folder.FLM_Guid] = {
          id: folder?.FLM_Guid,
          name: folder?.FLD_Name,
          path: folder?.FLD_FullPath,
          backgroundColor: "",
          messageId: response?.UMS_Guid,
        };
        return acc;
      }, {}),
    },
    allIds: Array.from(
      new Set([
        ...(oldMessageFolders?.allIds || []),
        ...(response?.FolderMessageInfo?.map((folder) => folder.FLM_Guid) ||
          []),
      ])
    ),
  };
};

export const mapCaseMessage = (
  caseMessage: CaseMessageDTO,
  oldMessage?: Message | null | undefined
): Message => {
  return {
    ...((oldMessage ?? {}) as Message),
    id: caseMessage.UMS_Guid,
    messageKey: caseMessage.MSG_Key,
    type: caseMessage.MSG_MST_Id,
    inOut: caseMessage.MSG_InOut,
    attachmentsCount: caseMessage.MSG_AttachmentsCount,
    from: caseMessage.MSG_From,
    to: caseMessage.MSG_To,
    cc: caseMessage?.MSG_Cc,
    subject: caseMessage.MSG_Subject,
    avatarName: calculateAvatarName(caseMessage.linkedUserName),
    sentDate: caseMessage.MSG_CreatedDateTime,
    username: caseMessage.linkedUserName,
  };
};
