import React, { useState, useEffect, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ActivityIndicator, View, StyleSheet } from "react-native";
import dayjs from "dayjs";
import { CustomText, useThemeAwareObject, Theme } from "b-ui-lib";
import BottomSheet from "@gorhom/bottom-sheet";
import {
  performDeleteAction,
  performMarkAsReadUnreadAction,
  fetchGridMessages,
  flagMessages,
  performUnDeleteAction,
  clearCopyOrMoveMessageFolderError,
  clearCopyOrMoveMessageFolderSuccess,
  clearFilteredEmailIds,
  clearAllErrors,
} from "../slices/gridMessageSlice";
import {
  setBottomSheetMenuState,
  longTapToSelectEmail,
  tapToSelectAdditionalEmail,
  deselectEmail,
  cancelMultiSelection,
  selectAllEmails,
  clearAllSelections,
  scrollToHideHeader,
  scrollToRevealHeader,
  clearInboxSearchFields,
  setInboxSearchFields,
} from "../slices/generalSlice";
import { getFolders } from "../slices/gridMessageSlice";
import {
  getUserEmailAdresses,
  getUserUniboxes,
} from "../slices/usersMailDomainSlice";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { SCREEN_NAMES } from "../constants/screenNames";
import { EMAIL_CASES } from "../constants/emailCases";
import { calculateSearchFiltersCount } from "../constants/gridMessagesSearchFields";
import { Message } from "../types/message";
import { addSearchSuggestion } from "../slices/searchFiltersSlice";

// Components
import InboxScreen from "../components/inbox/InboxScreen";
import Toast from "react-native-toast-message";
import InboxSearchHeader from "../navigation/headers/InboxSearchHeader";
import { fetchParticipantUsers } from "../slices/generalNotificationsSlice";
import SearchBarAnimationHOC from "./SearchBarAnimationHOC";
import { fetchGridCases } from "../slices/casesSlice";
import { TEST_IDS } from "../constants/testIds";
import { FLAGGED_API_VALUES } from "../constants/apiValues";

export const BOTTOM_SHEET_MENU_STATES = {
  default: "default",
  more: "more",
};

export type BottomSheetMenuState =
  (typeof BOTTOM_SHEET_MENU_STATES)[keyof typeof BOTTOM_SHEET_MENU_STATES];

type SectionListData = {
  title: string;
  data: Message[];
};

const denormalizeGridMessages = (
  gridMessages: { byId: Record<string, Message>; allIds: string[] },
  selectedFolderEmailIds: string[]
): SectionListData[] => {
  const { byId } = gridMessages;

  // Create an array of messages from normalized data, filtering out any undefined messages
  const messages: Message[] = selectedFolderEmailIds
    ?.map((id) => byId[id])
    ?.filter(Boolean) || [];

  // Sort messages by sentDate in descending order (newest first)
  const sortedMessages = messages.sort((a, b) => {
    const dateA = dayjs(a.sentDate);
    const dateB = dayjs(b.sentDate);
    return dateB.valueOf() - dateA.valueOf(); // Descending order
  });

  // Group messages by a formatted date using dayjs
  const sections: { [key: string]: Message[] } = {};

  sortedMessages.forEach((message) => {
    const sectionKey = dayjs(message.sentDate).format("DD MMM YYYY"); // Formatting as 'DD MMM YYYY'
    if (!sections[sectionKey]) {
      sections[sectionKey] = [];
    }
    sections[sectionKey].push(message);
  });

  // Transform the sections object into an array of SectionListData
  // Sort sections by date in descending order (newest dates first)
  const sectionEntries = Object.entries(sections).sort(([keyA], [keyB]) => {
    const dateA = dayjs(keyA, "DD MMM YYYY");
    const dateB = dayjs(keyB, "DD MMM YYYY");
    return dateB.valueOf() - dateA.valueOf(); // Descending order
  });

  return sectionEntries.map(([key, data]) => ({
    title: key,
    data: data, // Messages within each section are already sorted
  }));
};

export const INITIAL_PAGE_SIZE = 100;

const InboxHOC: React.FC = () => {
  const {
    gridMessages,
    folders,
    selectedFolderId,
    gridMessagesLoading,
    gridMessagesError,
    gridMessageCount,
    gridMessagesLoadingMore,
    appliedSearchFilters,
    copyOrMoveMessageFolderError,
    copyOrMoveMessageFolderSuccess,
  } = useSelector((state) => state.persist.gridMessageSlice);
  const { styles, color } = useThemeAwareObject(createStyles);
  const searchFiltersCount = calculateSearchFiltersCount(appliedSearchFilters);

  // If the user applies a search filter, we want to display emails from a different list
  const emailIds =
    searchFiltersCount > 0
      ? folders.byId[selectedFolderId]?.filteredEmailIds
      : folders.byId[selectedFolderId]?.emailIds;

  const sectionListData = denormalizeGridMessages(gridMessages, emailIds);

  const {
    bottomSheetMenuState,
    isMultiSelectActive,
    selectedMessageIds,
    isHeaderVisible,
  } = useSelector((state) => state.root.bTeamGeneralSlice);

  const isDeletedFolder = folders.allIds.find(
    (id) => folders.byId[id].name === "Deleted Items"
  );

  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const selectedMessages = useMemo(() => {
    const allMessages = sectionListData.flatMap((section) => section.data);
    return allMessages.filter((message) =>
      selectedMessageIds?.includes(message.id)
    );
  }, [sectionListData, selectedMessageIds]);

  const hasSelectedReadMessages = useMemo(() => {
    return selectedMessages.some((message) => message.isViewed);
  }, [selectedMessages]);

  const hasSelectedUnReadMessages = useMemo(() => {
    return selectedMessages.some((message) => !message.isViewed);
  }, [selectedMessages]);

  const hasSelectedOnlyUnFlaggedMessages = useMemo(() => {
    return selectedMessages.every((message) => !message.isFlagged);
  }, [selectedMessages]);

  useEffect(() => {
    // Clear any existing errors when component mounts
    dispatch(clearAllErrors());

    setTimeout(() => dispatch(getFolders()), 1);
    setTimeout(() => dispatch(getUserUniboxes()), 3);
    setTimeout(() => dispatch(getUserEmailAdresses()), 4);
    setTimeout(() => fetchParticipantUsersAction(), 5);
    setTimeout(() => dispatch(fetchGridCases(0)), 5);
  }, []);

  useEffect(() => {
    if (selectedFolderId) {
      _handleFetchGridMessages();
    }
  }, [selectedFolderId]);

  const _handleFetchGridMessages = () =>
    dispatch(fetchGridMessages({ folderGuid: selectedFolderId }));

  const fetchParticipantUsersAction = () => {
    dispatch(fetchParticipantUsers());
  };

  // Redux action dispatchers
  const handleSetBottomSheetMenuState = (
    bottomSheetState: BottomSheetMenuState
  ) => {
    dispatch(setBottomSheetMenuState(bottomSheetState));
  };

  const handleLongTapToSelectEmail = (id: string) => {
    dispatch(longTapToSelectEmail(id));
  };

  const handleTapToSelectAdditionalEmail = (id: string) => {
    dispatch(tapToSelectAdditionalEmail(id));
  };

  const handleDeselectEmail = (id: string) => {
    dispatch(deselectEmail(id));
  };

  const handleCancelMultiSelection = () => {
    dispatch(cancelMultiSelection());
  };

  const handleDeleteSelectedEmails = (payload: {
    ids: string[];
    shouldCloseMultiSelect?: boolean;
  }) => {
    dispatch(performDeleteAction(payload));
  };

  const handleUnDeleteSelectedEmails = (payload: {
    ids: string[];
    shouldCloseMultiSelect?: boolean;
  }) => {
    dispatch(performUnDeleteAction(payload));
  };

  const handleMarkAsReadUnread = () => {
    let mode;
    if (selectedMessages.length === 1) {
      // For a single message, check its status directly.
      mode = selectedMessages[0].isViewed ? 7 : 6;
    } else {
      // For multiple messages, if every selected message is unread, mark as read.
      // Otherwise (i.e. if any message is already read), mark as unread.
      mode = selectedMessages.every((message) => !message.isViewed) ? 6 : 7;
    }

    dispatch(
      performMarkAsReadUnreadAction({
        ids: selectedMessageIds,
        mode,
        shouldCloseMultiSelect: true,
      })
    );
  };

  const handleSelectAllEmails = () => {
    dispatch(selectAllEmails(emailIds));
  };

  const handleClearAllSelections = () => {
    dispatch(clearAllSelections());
  };

  const handleScrollToHideHeader = () => {
    dispatch(scrollToHideHeader());
  };

  const handleScrollToRevealHeader = () => {
    dispatch(scrollToRevealHeader());
  };

  const handleFlagMessage = (id?: string) => {
    // Single message selected case
    if (id) {
      // For a single message, check its status directly.
      const value = gridMessages.byId[id]?.isFlagged
        ? FLAGGED_API_VALUES.unFlag
        : FLAGGED_API_VALUES.flag;
      return dispatch(
        flagMessages({ ids: [id], value, shouldCloseMultiSelect: true })
      );
    }

    // Multiselect message case
    if (selectedMessageIds?.length > 0) {
      // For multiple messages, if every selected message is unFlagged, then flag.
      // Otherwise, (i.e. if any message is already flagged), then unFlagged.
      const value = hasSelectedOnlyUnFlaggedMessages
        ? FLAGGED_API_VALUES.flag
        : FLAGGED_API_VALUES.unFlag;
      dispatch(
        flagMessages({
          ids: selectedMessageIds,
          value,
          shouldCloseMultiSelect: true,
        })
      );
    }
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const clearCopyOrMoveMessageFolderSuccessAction = () => {
    dispatch(clearCopyOrMoveMessageFolderSuccess());
  };

  useEffect(() => {
    if (!copyOrMoveMessageFolderSuccess && !copyOrMoveMessageFolderError)
      return;

    if (copyOrMoveMessageFolderSuccess || copyOrMoveMessageFolderError) {
      Toast.show({
        type: copyOrMoveMessageFolderSuccess ? "success" : "error",
        text1: copyOrMoveMessageFolderSuccess
          ? copyOrMoveMessageFolderSuccess
          : copyOrMoveMessageFolderError,
        onPress() {
          Toast.hide();
        },
        onHide: () => {
          copyOrMoveMessageFolderSuccess
            ? clearCopyOrMoveMessageFolderSuccessAction()
            : clearCopyOrMoveMessageFolderErrorAction();
        },
        position: "bottom",
      });
    }
  }, [copyOrMoveMessageFolderSuccess, copyOrMoveMessageFolderError]);

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: true,
      messageId: null,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: true,
      messageId: null,
    });
  };

  const handleFindRelatedMessages = () => {
    // Only navigate if exactly one message is selected
    if (selectedMessageIds?.length === 1) {
      navigation.navigate(SCREEN_NAMES.relatedMessages, {
        UMS_Guid: selectedMessageIds?.[0],
      });
    }
  };

  const handleTapMessage = (messageId: string) => {
    navigation.navigate(SCREEN_NAMES.message, {
      UMS_Guid: messageId,
      emailIds,
    });
  };

  const BOTTOM_SHEET_DEFAULT_OPTIONS = [
    {
      title:
        !hasSelectedReadMessages && hasSelectedUnReadMessages
          ? "Mark as read"
          : "Mark as unread",
      onPress: () => {
        !hasSelectedReadMessages && hasSelectedUnReadMessages
          ? handleMarkAsReadUnread()
          : handleMarkAsReadUnread();
      },
      icon: "",
      testID: TEST_IDS.inboxBottomSheetMarkAsReadButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: isDeletedFolder === selectedFolderId ? "Restore" : "Delete",
      onPress: () =>
        isDeletedFolder === selectedFolderId
          ? handleUnDeleteSelectedEmails({
              ids: selectedMessageIds,
              shouldCloseMultiSelect: true,
            })
          : handleDeleteSelectedEmails({
              ids: selectedMessageIds,
              shouldCloseMultiSelect: true,
            }),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetDeleteButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "More",
      onPress: () => {
        bottomSheetRef.current?.snapToIndex(1);
        setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.more);
      },
      icon: "",
      testID: TEST_IDS.inboxBottomSheetMoreButton,
      isDisabled: false,
    },
  ];

  const BOTTOM_SHEET_MORE_OPTIONS = [
    {
      title:
        !hasSelectedReadMessages && hasSelectedUnReadMessages
          ? "Mark as read"
          : "Mark as unread",
      onPress: () => {
        handleMarkAsReadUnread();
      },
      icon: "mail",
      testID: TEST_IDS.inboxBottomSheetMarkAsReadMoreButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Move to folder",
      onPress: () => handleMoveSelectedEmailsToFolder(),
      icon: "folder",
      testID: TEST_IDS.inboxBottomSheetMoveToFolderButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Copy to folder",
      onPress: () => handleCopySelectedEmailsToFolder(),
      icon: "folder-plus",
      testID: TEST_IDS.inboxBottomSheetCopyToFolderButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Link to a case",
      onPress: () => {},
      icon: "briefcase",
      testID: TEST_IDS.inboxBottomSheetLinkToCaseButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: hasSelectedOnlyUnFlaggedMessages ? "Flag" : "Unflag",
      onPress: () => {
        handleFlagMessage();
      },
      icon: "flag",
      testID: TEST_IDS.inboxBottomSheetFlagButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Find Related Messages",
      onPress: () => {
        if (selectedMessageIds?.length === 1) {
          handleFindRelatedMessages();
        } else {
          // Show toast message when no message or multiple messages are selected
          Toast.show({
            type: "info",
            text1: "Please select exactly one message",
            text2:
              selectedMessageIds?.length === 0
                ? "You need to select a message first"
                : "You can only find related messages for one message at a time",
            position: "top",
          });
        }
      },
      icon: "search",
      // Disable the option when no message or multiple messages are selected
      isDisabled: selectedMessageIds?.length !== 1,
      testID: TEST_IDS.inboxBottomSheetFindRelatedButton,
    },
    {
      title: "Select All",
      onPress: () => handleSelectAllEmails(),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetSelectAllButton,
      isDisabled: emailIds?.length === selectedMessageIds?.length,
    },
    {
      title: `Unselect All (${selectedMessageIds?.length})`,
      onPress: () => handleClearAllSelections(),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetUnSelectAllButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
  ];

  const [skipFirst, setSkipFirst] = useState(0);
  const [pageSize, setPageSize] = useState(INITIAL_PAGE_SIZE);

  useEffect(() => {
    if (selectedFolderId) {
      resetMessages(); // Reset on folder change
    }
  }, [selectedFolderId]);

  const fetchMessages = async (offset: number, size: number) => {
    const params = {
      skipFirst: offset,
      pageSize: size,
    };

    await dispatch(fetchGridMessages(params));
  };

  // Infinite scroll load more function
  const loadMoreMessages = () => {
    if (
      !gridMessagesLoadingMore && // Prevent new fetch during initial load
      gridMessages.allIds.length < gridMessageCount // Ensure more data is available
    ) {
      const newSkip = skipFirst + pageSize;

      fetchMessages(newSkip, pageSize).finally(() => {
        setSkipFirst(newSkip); // Update skip value after fetch
      });
    }
  };

  const resetMessages = () => {
    setSkipFirst(0);
    setPageSize(INITIAL_PAGE_SIZE);
    fetchMessages(0, INITIAL_PAGE_SIZE);
  };

  // SEARCH START
  const { searchSuggestions } = useSelector(
    (state) => state.persist.searchFiltersSlice.messages
  );

  const { inboxSearchFields } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );

  const clearInboxSearchFieldsAction = () => {
    dispatch(clearInboxSearchFields());
  };

  const clearFilteredEmailIdsAction = () => dispatch(clearFilteredEmailIds());

  const setInboxSearchFieldsAction = (fields: {}) => {
    dispatch(setInboxSearchFields(fields));
  };

  const fetchGridMessagesAction = () =>
    dispatch(fetchGridMessages({ folderGuid: selectedFolderId }));

  const addSearchSuggestionAction = (suggestion: string) =>
    dispatch(addSearchSuggestion(suggestion));

  // SEARCH END

  return (
    <SearchBarAnimationHOC
      searchBar={
        <InboxSearchHeader
          navigation={navigation}
          inboxSearchFields={inboxSearchFields}
          appliedSearchFilters={appliedSearchFilters}
          searchFiltersCount={searchFiltersCount}
          clearInboxSearchFields={clearInboxSearchFieldsAction}
          clearFilteredEmailIds={clearFilteredEmailIdsAction}
          setInboxSearchFields={setInboxSearchFieldsAction}
          fetchGridMessages={fetchGridMessagesAction}
          searchSuggestions={searchSuggestions}
          addSearchSuggestion={addSearchSuggestionAction}
          searchInputRef={React.createRef()}
        />
      }
    >
      <InboxScreen
        sections={sectionListData}
        isHeaderVisible={isHeaderVisible}
        onScrollToHideHeader={handleScrollToHideHeader}
        onScrollToRevealHeader={handleScrollToRevealHeader}
        isMultiSelectActive={isMultiSelectActive}
        selectedMessageIds={selectedMessageIds}
        onLongTapToSelectEmail={handleLongTapToSelectEmail}
        onTapToSelectAdditionalEmail={handleTapToSelectAdditionalEmail}
        onDeselectEmail={handleDeselectEmail}
        onSelectAllEmails={handleSelectAllEmails}
        onClearAllSelections={handleClearAllSelections}
        onCancelMultiSelection={handleCancelMultiSelection}
        onFlagPress={handleFlagMessage}
        handleTapMessage={handleTapMessage}
        handleRefreshList={_handleFetchGridMessages}
        isLoading={gridMessagesLoading}
        bottomSheetRef={bottomSheetRef}
        bottomSheetMenuState={bottomSheetMenuState}
        setBottomSheetMenuState={handleSetBottomSheetMenuState}
        bottomSheetOptions={BOTTOM_SHEET_DEFAULT_OPTIONS}
        bottomSheetMoreOptions={BOTTOM_SHEET_MORE_OPTIONS}
        errorMessage={
          gridMessagesError ? "Something went wrong:\n" + gridMessagesError : ""
        }
        emptyMessage={
          searchFiltersCount > 0 ? "No results found" : "List Empty"
        }
        inboxListStyle={[styles.listContainer]}
        onLayout={() => {}}
        initialNumToRender={20}
        onScroll={() => {}}
        listFooterComponent={
          gridMessages.allIds.length < gridMessages.total ||
          gridMessagesLoadingMore ? (
            <View style={{ padding: 10 }}>
              {gridMessagesLoadingMore && (
                <ActivityIndicator size="small" color={color.MESSAGE_FLAG} />
              )}
            </View>
          ) : (
            <View style={{ padding: 10 }}>
              {emailIds?.length > 0 && (
                <CustomText style={{ textAlign: "center" }}>
                  No more items to show
                </CustomText>
              )}
            </View>
          )
        }
        loadMoreEmails={loadMoreMessages} // Load more on scroll
      />
    </SearchBarAnimationHOC>
  );
};

export default InboxHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    searchBar: {
      justifyContent: "center",
      backgroundColor: "white",
      width: "100%",
      top: 0,
      zIndex: 1000,
    },
    input: {
      height: 40,
      backgroundColor: "#f0f0f0",
      borderRadius: 8,
      paddingHorizontal: 10,
    },
    listContainer: {
      flex: 1,
    },
    item: {
      padding: 20,
    },
    header: {
      padding: 10,
      backgroundColor: "#ddd",
    },
  });

  return { styles, color };
};
