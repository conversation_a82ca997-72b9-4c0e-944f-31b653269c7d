import React, { useEffect, useMemo, useCallback } from "react";
import { <PERSON>, BackHand<PERSON>, useWindowDimensions } from "react-native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import {
  clearCopyOrMoveMessageFolderError,
  flagMessages,
  getMessageActions,
  getMessageActionsNext,
  getMessageActionsPrevious,
  getMessageBody,
  getMessageBodyNext,
  getMessageBodyPrevious,
  getMessageComments,
  getMessageMetadata,
  performDeleteAction,
  performMarkAsReadUnreadAction,
  performUnDeleteAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { sendMessageClearStatus } from "../slices/generalSlice";
import { Message } from "../types/message";
import { MessageActionTypes } from "../types/MessageActionTypes";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateAvatarName } from "../helpers/calculateAvatarName";
// Components
import CommentsHOC from "./CommentsHOC";
import MessageScreen from "../components/message/MessageScreen";

// Constants
import { EMAIL_CASES } from "../constants/emailCases";
import { SCREEN_NAMES } from "../constants/screenNames";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";

import {
  PanGestureHandler,
  GestureHandlerRootView,
} from "react-native-gesture-handler";
import Animated, {
  useSharedValue,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from "react-native-reanimated";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { FOLDER_NAMES } from "../constants/folderNames";

type Props = {};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

const MessageHOC: React.FC = ({}: Props) => {
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const initialUMS_Guid = route.params?.UMS_Guid;
  const emailIds: string[] = route.params?.emailIds || [];
  const initialIndex = emailIds.findIndex((id) => id === initialUMS_Guid);
  const [currentEmailIndex, setCurrentEmailIndex] =
    React.useState(initialIndex);
  const [isTransitioning, setIsTransitioning] = React.useState(false);
  const currentUMS_Guid = emailIds[currentEmailIndex];
  const { casesList } = useSelector((state) => state.persist.bTeamCasesSlice);

  const message: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages.byId[currentUMS_Guid]
  );

  const messages: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages
  );

  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const { attachments, downloadedAttachments } = useSelector(
    (state) => state.root.bTeamAttachmentsSlice
  );

  const {
    folders,
    selectedFolderId,
    getMessageBodyLoading,
    getMessageCommentsLoading,
    getMessageActionsLoading,
    getMessageBodyError,
    getMessageCommentsError,
    getMessageActionsError,
    copyOrMoveMessageFolderError,
    messageFolders,
  } = useSelector((state) => state.persist.gridMessageSlice);

  const prevId = currentEmailIndex > 0 ? emailIds[currentEmailIndex - 1] : null;
  const nextId =
    currentEmailIndex < emailIds.length - 1
      ? emailIds[currentEmailIndex + 1]
      : null;
  const { sendDraftMessageSuccess } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );
  const isDeletedFolder =
    folders.allIds.find(
      (id) => folders.byId[id].name === FOLDER_NAMES.deletedItems
    ) === selectedFolderId;

  const cases = useMemo(() => {
    return (
      message?.caseIds
        ?.map((id) => {
          const item = casesList.byId[id];

          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || []
    );
  }, [message?.caseIds, casesList.byId]);

  const handleDeleteMessage = () => {
    dispatch(performDeleteAction({ ids: [currentUMS_Guid] }));
    navigation.goBack();
  };

  useFocusEffect(
    useCallback(() => {
      translateX.value = -width;
    }, [currentUMS_Guid, width])
  );

  const handleUnDeleteMessage = () => {
    dispatch(performUnDeleteAction({ ids: [currentUMS_Guid] }));
    navigation.goBack();
  };

  const handleFlagMessageAction = () => {
    dispatch(
      flagMessages({
        ids: [currentUMS_Guid],
        value: message.isFlagged
          ? FLAGGED_API_VALUES.unFlag
          : FLAGGED_API_VALUES.flag,
      })
    );
  };

  const handleMarkAsReadUnreadAction = () => {
    // If message is viewed (read), mark as unread (mode 7)
    // If message is not viewed (unread), mark as read (mode 6)
    const mode = message?.isViewed ? 7 : 6;
    dispatch(performMarkAsReadUnreadAction({ ids: [currentUMS_Guid], mode }));
  };

  const handleGetMessageActions = () =>
    dispatch(getMessageActions({ UMS_Guid: currentUMS_Guid }));

  const setDownloadedAttachmentsAction = (payload) => {
    dispatch(setDownloadedAttachments(payload));
  };

  const clearDownloadedAttachmentsAction = () => {
    dispatch(clearDownloadedAttachments());
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: false,
      messageId: currentUMS_Guid,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: false,
      messageId: currentUMS_Guid,
    });
  };

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", () => {
      navigation.goBack();
      return true;
    });
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      deleteMessage: isDeletedFolder
        ? handleUnDeleteMessage
        : handleDeleteMessage,
      handleFlagMessage: handleFlagMessageAction,
      handleMarkAsReadUnread: handleMarkAsReadUnreadAction,
    });
  }, [navigation, message, isDeletedFolder]);

  useEffect(() => {
    if (!message?.fullMessageBody) {
      dispatch(getMessageBody({ UMS_Guid: currentUMS_Guid }));
    }
    dispatch(getMessageComments({ guid: currentUMS_Guid, entityId: 1001 }));
    dispatch(getMessageMetadata({ guid: currentUMS_Guid }));
    dispatch(getMessageActions({ UMS_Guid: currentUMS_Guid }));
  }, [currentUMS_Guid, message?.fullMessageBody]);

  // Preload body, metadata, and actions for prevId and nextId messages when available, but only fetch body if not already loaded
  useEffect(() => {
    if (prevId) {
      const prevMessage = messages.byId[prevId];
      if (!prevMessage?.fullMessageBody) {
        dispatch(getMessageBodyPrevious({ UMS_Guid: prevId }));
      }
      dispatch(getMessageActionsPrevious({ UMS_Guid: prevId }));
    }

    if (nextId) {
      const nextMessage = messages.byId[nextId];
      if (!nextMessage?.fullMessageBody) {
        dispatch(getMessageBodyNext({ UMS_Guid: nextId }));
      }
      dispatch(getMessageActionsNext({ UMS_Guid: nextId }));
    }
  }, [prevId, nextId]);

  useFocusEffect(
    useCallback(() => {
      if (sendDraftMessageSuccess) dispatch(sendMessageClearStatus());
    }, [sendDraftMessageSuccess])
  );

  useEffect(() => {
    dispatch(
      performMarkAsReadUnreadAction({
        ids: [currentUMS_Guid],
        mode: 6,
      })
    );
  }, [currentUMS_Guid]);

  const repliesData = useMemo(
    () =>
      message?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(message?.repliedByUser),
              from: "",
              username: message?.repliedByUser,
              date: message?.timeReplied,
            },
          ]
        : [],
    [message?.repliedByUser, message?.timeReplied]
  );

  const handleFilesDownloadStatus = (
    attachmentId: string,
    downloadStatus: FileDownloadStatus
  ) => {
    setDownloadedAttachmentsAction({
      id: attachmentId,
      status: downloadStatus,
      isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
    });
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    clearDownloadedAttachments();

    await downloadSingleAttachment(
      token,
      attachmentId,
      attachments?.byId?.[attachmentId]?.name,
      handleFilesDownloadStatus
    );
  };

  const handleDownloadAllAttachments = async (attachmentIds: string[]) => {
    // Clear any existing downloaded attachments.
    clearDownloadedAttachments();

    // If there are no attachments, we can optionally return or alert.
    if (attachmentIds.length === 0) {
      console.log("No attachments found");
      return;
    }

    const fileNamesMapping: Record<string, string> = {};
    if (attachments?.byId) {
      Object.keys(attachments.byId).forEach((id) => {
        // Ensure that the object has a "name" property.
        fileNamesMapping[id] = attachments.byId[id].name;
      });
    }

    // Download all attachments.
    await downloadMultipleAttachments(
      token,
      attachmentIds,
      fileNamesMapping,
      handleFilesDownloadStatus
    );
  };

  const isAnyFileDownloadLoading = useMemo(
    () =>
      Object.values(downloadedAttachments).some(
        (status) => status === FILE_DOWNLOAD_STATUS.loading
      ),
    [downloadedAttachments]
  );

  const handleEditDraftMessage = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.DraftEdit,
    });
  };

  const handleOnPressReply = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.Reply,
    });
  };

  const handleOnPressReplyAll = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.ReplyAll,
    });
  };

  const handleOnPressForward = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.Forward,
    });
  };

  const translateX = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx: any) => {
      ctx.startX = translateX.value;
    },
    onActive: (event, ctx: any) => {
      let nextTranslateX = ctx.startX + event.translationX;

      // Prevent over-dragging beyond the first message
      if (currentEmailIndex === 0 && nextTranslateX > -width) {
        nextTranslateX = -width;
      }

      // Prevent over-dragging beyond the last message
      if (
        currentEmailIndex === emailIds.length - 1 &&
        nextTranslateX < -width
      ) {
        nextTranslateX = -width;
      }

      translateX.value = nextTranslateX;
    },
    onEnd: (event) => {
      const threshold = width * 0.2;

      const isSwipeLeft = event.translationX < -threshold;
      const isSwipeRight = event.translationX > threshold;

      if (isSwipeLeft && currentEmailIndex < emailIds.length - 1) {
        runOnJS(setIsTransitioning)(true);
        const newIndex = currentEmailIndex + 1;
        runOnJS(setCurrentEmailIndex)(newIndex);
        translateX.value = withTiming(-2 * width, { duration: 300 }, () => {
          translateX.value = -width;
          runOnJS(setIsTransitioning)(false);
        });
      } else if (isSwipeRight && currentEmailIndex > 0) {
        runOnJS(setIsTransitioning)(true);
        const newIndex = currentEmailIndex - 1;
        runOnJS(setCurrentEmailIndex)(newIndex);
        translateX.value = withTiming(0, { duration: 300 }, () => {
          translateX.value = -width;
          runOnJS(setIsTransitioning)(false);
        });
      } else {
        translateX.value = withTiming(-width, { duration: 300 });
        // Don't show loader, because message index doesn't change
      }
    },
  });

  // Lightweight preview card for left/right adjacent messages
  const renderPreviewCard = (previewMessage: Message | null) => {
    if (!previewMessage) return null;

    return (
      <View style={{ flex: 1, justifyContent: "center" }}>
        <MessageScreen
          width={width}
          message={previewMessage}
          replies={repliesData}
          messageAttachmentsIds={previewMessage?.attachmentsIds}
          attachments={attachments}
          attachmentsCount={previewMessage?.attachmentsCount}
          downloadedAttachments={downloadedAttachments}
          foldersIds={previewMessage?.foldersIds}
          messageFolders={messageFolders}
          caseMetadata={previewMessage?.messageMetadata}
          cases={cases}
          getMessageBodyLoading={getMessageBodyLoading}
          getMessageCommentsLoading={getMessageCommentsLoading}
          getMessageActionsLoading={getMessageActionsLoading}
          messageBodyError={getMessageBodyError}
          getMessageCommentsError={getMessageCommentsError}
          getMessageActionsError={getMessageActionsError}
          copyOrMoveMessageFolderError={copyOrMoveMessageFolderError}
          moveToFolder={handleMoveSelectedEmailsToFolder}
          copyToFolder={handleCopySelectedEmailsToFolder}
          getMessageActions={handleGetMessageActions}
          setDownloadedAttachments={setDownloadedAttachmentsAction}
          clearDownloadedAttachments={clearDownloadedAttachmentsAction}
          handleClearCopyOrMoveMessageFolderError={
            clearCopyOrMoveMessageFolderErrorAction
          }
          handleDownloadAttachment={handleDownloadAttachment}
          handleDownloadAllAttachments={handleDownloadAllAttachments}
          isAnyFileDownloadLoading={isAnyFileDownloadLoading}
          isDraft={previewMessage?.inOut === 3}
          handleEditDraftMessage={handleEditDraftMessage}
          onPressReply={handleOnPressReply}
          onPressReplyAll={handleOnPressReplyAll}
          onPressForward={handleOnPressForward}
          isDummy
        />
      </View>
    );
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View
          style={[
            { flex: 1, flexDirection: "row", width: width * 3 },
            animatedStyle,
          ]}
        >
          {/* Left Preview */}
          <View style={{ width }}>
            {currentEmailIndex > 0 &&
              renderPreviewCard(messages.byId[emailIds[currentEmailIndex - 1]])}
          </View>

          {/* Current Message */}
          <View style={{ width }}>
            <CommentsHOC
              guid={emailIds[currentEmailIndex]}
              entityId="1001"
              commentIds={
                messages.byId[emailIds[currentEmailIndex]]?.messageCommentIds
              }
              refreshListCallback={() =>
                dispatch(
                  getMessageComments({
                    guid: emailIds[currentEmailIndex],
                    entityId: 1001,
                  })
                )
              }
            >
              <MessageScreen
                width={width}
                message={messages.byId[emailIds[currentEmailIndex]]}
                replies={repliesData}
                messageAttachmentsIds={
                  messages.byId[emailIds[currentEmailIndex]]?.attachmentsIds
                }
                attachments={attachments}
                attachmentsCount={
                  messages.byId[emailIds[currentEmailIndex]]?.attachmentsCount
                }
                downloadedAttachments={downloadedAttachments}
                foldersIds={
                  messages.byId[emailIds[currentEmailIndex]]?.foldersIds
                }
                messageFolders={messageFolders}
                caseMetadata={
                  messages.byId[emailIds[currentEmailIndex]]?.messageMetadata
                }
                cases={cases}
                getMessageBodyLoading={getMessageBodyLoading || isTransitioning}
                getMessageCommentsLoading={getMessageCommentsLoading}
                getMessageActionsLoading={getMessageActionsLoading}
                messageBodyError={getMessageBodyError}
                getMessageCommentsError={getMessageCommentsError}
                getMessageActionsError={getMessageActionsError}
                copyOrMoveMessageFolderError={copyOrMoveMessageFolderError}
                moveToFolder={handleMoveSelectedEmailsToFolder}
                copyToFolder={handleCopySelectedEmailsToFolder}
                getMessageActions={handleGetMessageActions}
                setDownloadedAttachments={setDownloadedAttachmentsAction}
                clearDownloadedAttachments={clearDownloadedAttachmentsAction}
                handleClearCopyOrMoveMessageFolderError={
                  clearCopyOrMoveMessageFolderErrorAction
                }
                handleDownloadAttachment={handleDownloadAttachment}
                handleDownloadAllAttachments={handleDownloadAllAttachments}
                isAnyFileDownloadLoading={isAnyFileDownloadLoading}
                isDraft={
                  messages.byId[emailIds[currentEmailIndex]]?.inOut === 3
                }
                handleEditDraftMessage={handleEditDraftMessage}
                onPressReply={handleOnPressReply}
                onPressReplyAll={handleOnPressReplyAll}
                onPressForward={handleOnPressForward}
              />
            </CommentsHOC>
          </View>

          {/* Right Preview */}
          <View style={{ width }}>
            {currentEmailIndex < emailIds.length - 1 &&
              renderPreviewCard(messages.byId[emailIds[currentEmailIndex + 1]])}
          </View>
        </Animated.View>
      </PanGestureHandler>
    </GestureHandlerRootView>
  );
};

export default MessageHOC;
